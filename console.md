> js_vanishpost@0.1.0 dev
> next dev

   ▲ Next.js 15.3.0
   - Local:        http://localhost:3000
   - Network:      http://**********:3000
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 4.1s
 ○ Compiling /middleware ...
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
RendererToggle props: {
  rendererType: 'react-iframe',
  currentScale: 100,
  showZoomControls: false,
  hasZoomIn: true,
  hasZoomOut: true,
  hasResetZoom: true
}
 GET / 200 in 7531ms
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized config cache',
  timestamp: '2025-07-09T08:15:34.810Z',
  metadata: undefined
}
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized domain cache',
  timestamp: '2025-07-09T08:15:34.825Z',
  metadata: undefined
}
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized ad configuration cache',
  timestamp: '2025-07-09T08:15:34.826Z',
  metadata: undefined
}
 POST /api/analytics/session 404 in 10ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET / 200 in 6312ms
[INFO] [CACHE_INIT] Initialized domain cache undefined
[INFO] [CACHE_INIT] Initialized config cache undefined
[INFO] [CACHE_INIT] Initialized ad configuration cache undefined
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Successfully initialized 3 of 3 caches',
  timestamp: '2025-07-09T08:15:36.458Z',
  metadata: undefined
}
 GET /management-portal-x7z9y2/login 200 in 7168ms
 GET /api/admin/cleanup/status 200 in 7892ms
[INFO] [CACHE_INIT] Successfully initialized 3 of 3 caches undefined
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized 3 caches',
  timestamp: '2025-07-09T08:15:36.848Z'
  metadata: undefined
}
[INFO] [CACHE_INIT] Initialized 3 caches undefined
 POST /api/admin/cache/init 200 in 8266ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET / 200 in 6179ms
 POST /api/admin/cleanup/scheduler 200 in 5422ms
 GET /api/analytics/session?sessionId=session_1752048928420_14pyon5geeg_nmv3ws 404 in 6648ms
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 POST /api/analytics/heartbeat 404 in 7ms
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 POST /api/analytics/heartbeat 404 in 5ms
 POST /api/analytics/heartbeat 404 in 2ms
 GET /management-portal-x7z9y2/login 200 in 472ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized config cache',
  timestamp: '2025-07-09T08:16:33.734Z',
  metadata: undefined
}
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized domain cache',
  timestamp: '2025-07-09T08:16:33.745Z',
  metadata: undefined
}
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized ad configuration cache',
  timestamp: '2025-07-09T08:16:33.749Z',
  metadata: undefined
}
 GET /api/admin/auth 200 in 5105ms
 GET /management-portal-x7z9y2/login 200 in 5446ms
[INFO] [CACHE_INIT] Initialized ad configuration cache undefined
[INFO] [CACHE_INIT] Initialized config cache undefined
 GET /api/admin/cleanup/status 200 in 6307ms
[INFO] [CACHE_INIT] Initialized domain cache undefined
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Successfully initialized 3 of 3 caches',
  timestamp: '2025-07-09T08:16:35.584Z',
  metadata: undefined
}
[INFO] [CACHE_INIT] Successfully initialized 3 of 3 caches undefined
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized 3 caches',
  timestamp: '2025-07-09T08:16:35.851Z',
  metadata: undefined
}
 POST /api/admin/cleanup/scheduler 200 in 382ms
[INFO] [CACHE_INIT] Initialized 3 caches undefined
 POST /api/admin/cache/init 200 in 6852ms
Successful login for username: admin from IP: ::1
 POST /api/admin/auth 200 in 1613ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET /management-portal-x7z9y2/monitoring 200 in 5806ms
 GET /management-portal-x7z9y2/monitoring 200 in 230ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET /api/management-portal-x7z9y2/monitoring 200 in 8984ms
Setting up log stream with filters: level=all, category=all
New connection added. Total connections: 1
Sending data to client: data: {"type":"connected","timestamp":"2025-07-09T08:17:27.895Z"}


Added log listener. Total listeners: 1
Log listener registered
 GET /api/management-portal-x7z9y2/monitoring 200 in 76ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET /management-portal-x7z9y2/analytics 200 in 15744ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized config cache',
  timestamp: '2025-07-09T08:19:36.749Z',
  metadata: undefined
}
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized domain cache',
  timestamp: '2025-07-09T08:19:36.754Z',
  metadata: undefined
}
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized ad configuration cache',
  timestamp: '2025-07-09T08:19:36.755Z',
  metadata: undefined
}
 GET /api/admin/auth 200 in 6717ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
[INFO] [CACHE_INIT] Initialized config cache undefined
 GET /api/management-portal-x7z9y2/maintenance/scheduler 200 in 7222ms
 GET /api/admin/cleanup/status 200 in 7228ms
[INFO] [CACHE_INIT] Initialized domain cache undefined
[INFO] [CACHE_INIT] Initialized ad configuration cache undefined
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Successfully initialized 3 of 3 caches',
  timestamp: '2025-07-09T08:19:37.863Z',
  metadata: undefined
}
[INFO] [CACHE_INIT] Successfully initialized 3 of 3 caches undefined
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized 3 caches',
  timestamp: '2025-07-09T08:19:38.241Z',
  metadata: undefined
}
[INFO] [CACHE_INIT] Initialized 3 caches undefined
 POST /api/admin/cache/init 200 in 8312ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 POST /api/admin/cleanup/scheduler 200 in 2210ms
 GET /api/management-portal-x7z9y2/analytics?timeRange=24h&aggregation=count 200 in 2582ms
 POST /api/management-portal-x7z9y2/maintenance/scheduler 200 in 2279ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET /api/management-portal-x7z9y2/analytics?timeRange=24h&aggregation=count 200 in 566ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET /api/management-portal-x7z9y2/analytics/sessions?limit=100 200 in 1293ms
 GET /api/management-portal-x7z9y2/analytics/sessions?limit=100 200 in 340ms
 GET /api/management-portal-x7z9y2/analytics?timeRange=24h&aggregation=timeline 200 in 324ms
 GET /api/management-portal-x7z9y2/analytics?startDate=2025-07-09T08:14:41.251Z&aggregation=count 200 in 317ms
 GET /api/management-portal-x7z9y2/analytics?startDate=2025-07-09T08:14:41.257Z&aggregation=count 200 in 338ms
 GET /api/management-portal-x7z9y2/analytics?timeRange=24h&aggregation=timeline 200 in 351ms
 GET /api/management-portal-x7z9y2/analytics/sessions?startDate=2025-07-09T08:14:41.251Z&limit=50 200 in 497ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET /api/management-portal-x7z9y2/analytics/sessions?startDate=2025-07-09T08:14:41.257Z&limit=50 200 in 1648ms
 GET /api/management-portal-x7z9y2/analytics/live-visitors?activeThreshold=3&includeDetails=false 200 in 1880ms
 GET /api/management-portal-x7z9y2/analytics/live-visitors?activeThreshold=3&includeDetails=false 200 in 971ms
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 POST /api/analytics/heartbeat 404 in 88ms
 POST /api/analytics/heartbeat 404 in 63ms
 POST /api/analytics/heartbeat 404 in 28ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET /api/management-portal-x7z9y2/analytics/live-visitors?activeThreshold=3&includeDetails=false 200 in 5947ms
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 POST /api/analytics/heartbeat 404 in 49ms
 POST /api/analytics/heartbeat 404 in 34ms
RendererToggle props: {
  rendererType: 'react-iframe',
  currentScale: 100,
  showZoomControls: false,
  hasZoomIn: true,
  hasZoomOut: true,
  hasResetZoom: true
}
 GET / 200 in 8542ms
 GET /api/management-portal-x7z9y2/analytics?startDate=2025-07-09T08:15:11.966Z&aggregation=count 200 in 8943ms
 GET /api/management-portal-x7z9y2/analytics/live-visitors?activeThreshold=3&includeDetails=false 200 in 9408ms
 GET /api/management-portal-x7z9y2/analytics/sessions?startDate=2025-07-09T08:15:11.966Z&limit=50 200 in 381ms
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 POST /api/analytics/session 404 in 3ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 GET /api/management-portal-x7z9y2/maintenance/scheduler 200 in 649ms
 GET /api/admin/cleanup/status 200 in 654ms
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized config cache',
  timestamp: '2025-07-09T08:20:36.041Z',
  metadata: undefined
}
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized domain cache',
  timestamp: '2025-07-09T08:20:36.059Z',
  metadata: undefined
}
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized ad configuration cache',
  timestamp: '2025-07-09T08:20:36.062Z',
  metadata: undefined
}
 GET /api/analytics/session?sessionId=session_1752049225203_zoetv05qiyl_ppjfxk 404 in 11135ms
[INFO] [CACHE_INIT] Initialized domain cache undefined
 POST /api/management-portal-x7z9y2/maintenance/scheduler 200 in 10041ms
[INFO] [CACHE_INIT] Initialized config cache undefined
[INFO] [CACHE_INIT] Initialized ad configuration cache undefined
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Successfully initialized 3 of 3 caches',
  timestamp: '2025-07-09T08:20:36.979Z',
  metadata: undefined
}
 POST /api/admin/cleanup/scheduler 200 in 9329ms
[INFO] [CACHE_INIT] Successfully initialized 3 of 3 caches undefined
Notifying 0 listeners of new log entry: {
  level: 'info',
  category: 'CACHE_INIT',
  message: 'Initialized 3 caches',
  timestamp: '2025-07-09T08:20:38.498Z',
  metadata: undefined
}
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
 ⚠ ./src/middleware.ts
Attempted import error: 'validateRequestSize' is not exported from './lib/analytics/rateLimiting' (imported as 'validateRequestSize').
[INFO] [CACHE_INIT] Initialized 3 caches undefined
 POST /api/admin/cache/init 200 in 13392ms
 POST /api/analytics/heartbeat 404 in 6ms
 GET /api/management-portal-x7z9y2/analytics/live-visitors?activeThreshold=3&includeDetails=false 200 in 12079ms
 GET /api/management-portal-x7z9y2/analytics?startDate=2025-07-09T08:15:41.266Z&aggregation=count 200 in 402ms
 GET /api/management-portal-x7z9y2/analytics/sessions?startDate=2025-07-09T08:15:41.266Z&limit=50 200 in 553ms
 GET /api/management-portal-x7z9y2/analytics/live-visitors?activeThreshold=3&includeDetails=false 200 in 1387ms
 ⨯ Error [TypeError]: (0 , _lib_analytics_rateLimiting__WEBPACK_IMPORTED_MODULE_3__.validateRequestSize) is not a function
    at handleAnalyticsEndpoint (src\middleware.ts:111:47)
    at middleware (src\middleware.ts:209:11)
  109 |   if (request.method === 'POST') {
  110 |     const contentLength = parseInt(request.headers.get('content-length') || '0');
> 111 |     const sizeValidation = validateRequestSize(contentLength);
      |                                               ^
  112 |
  113 |     if (!sizeValidation.valid) {
  114 |       return new NextResponse(
 POST /api/analytics/heartbeat 404 in 4ms